# Cập nhật Logic Đóng Lệnh Dựa Trên Config

## Tóm tắt thay đổi
Bot đã được cập nhật để tự động đóng tất cả các lệnh sau thời gian tối đa được cấu hình trong file config, mặc định là 25 phút thay vì 24 giờ như trước đây. Thời gian này có thể được tùy chỉnh thông qua file config.

## Các file đã được thay đổi

### 1. `config/config.yaml` và `config/config.example.yaml`
**Thay đổi:** Thêm cấu hình `max_position_age_minutes` vào phần `risk`.

```yaml
risk:
  # ... các cấu hình khác ...
  max_position_age_minutes: 25  # Maximum time to hold a position (minutes)
```

### 2. `src/utils/config.py`
**Thay đổi:** Thêm trường `max_position_age_minutes` vào class `RiskConfig`.

```python
@dataclass
class RiskConfig:
    # ... các trường khác ...
    max_position_age_minutes: int = 25  # Maximum time to hold a position (minutes)
```

### 3. `src/risk/risk_manager.py`
**Thay đổi:** Cập nhật logic để sử dụng giá trị từ config thay vì hard-code.

```python
# TRƯỚC:
if position_age > timedelta(minutes=25):
    return True, "Maximum position age exceeded (25 minutes)"

# SAU:
max_age_minutes = self.risk_config.max_position_age_minutes
if position_age > timedelta(minutes=max_age_minutes):
    return True, f"Maximum position age exceeded ({max_age_minutes} minutes)"
```

### 4. `src/backtesting/backtest_engine.py`
**Thay đổi:** Cập nhật logic để sử dụng giá trị từ config thay vì hard-code.

```python
# TRƯỚC:
if position_age.total_seconds() > 25 * 60:  # 25 minutes in seconds
    positions_to_close.append((i, current_price, 'Maximum position age exceeded (25 minutes)'))

# SAU:
max_age_minutes = self.config.risk.max_position_age_minutes
if position_age.total_seconds() > max_age_minutes * 60:  # Convert minutes to seconds
    positions_to_close.append((i, current_price, f'Maximum position age exceeded ({max_age_minutes} minutes)'))
```

### 5. `tests/test_risk_manager.py`
**Thay đổi:** Cập nhật test case và thêm config `max_position_age_minutes`.

```python
# Thêm vào RiskConfig trong test:
self.risk_config = RiskConfig(
    # ... các cấu hình khác ...
    max_position_age_minutes=25
)

# Cập nhật test case:
time=datetime.now() - timedelta(minutes=26)  # Older than 25 minutes
```

## Logic hoạt động

### Cách thức hoạt động:
1. **Kiểm tra định kỳ:** Bot kiểm tra tất cả các position mở mỗi 5 phút (theo cấu hình timeframe)
2. **Tính toán thời gian:** Tính thời gian từ khi position được mở đến hiện tại
3. **Điều kiện đóng:** Nếu thời gian > 25 phút, position sẽ được đóng tự động
4. **Áp dụng cho cả:** Live trading và backtesting

### Các trường hợp đóng lệnh:
- **< 25 phút:** Position tiếp tục mở
- **= 25 phút:** Position bị đóng (logic sử dụng `>` nên đúng 25 phút cũng bị đóng)
- **> 25 phút:** Position bị đóng

## Test cases đã được kiểm tra

✅ **Position 10 phút:** Không bị đóng
✅ **Position 24 phút:** Không bị đóng
✅ **Position 25 phút:** Bị đóng
✅ **Position 26 phút:** Bị đóng
✅ **Position 30 phút:** Bị đóng

## Tác động đến trading

### Ưu điểm:
- **Giảm rủi ro:** Hạn chế thời gian exposure của mỗi lệnh
- **Quản lý tốt hơn:** Tránh việc giữ lệnh quá lâu trong thị trường biến động
- **Tăng tính linh hoạt:** Cho phép bot mở lệnh mới thường xuyên hơn

### Lưu ý:
- **Backtest:** Logic này cũng áp dụng cho backtesting để đảm bảo tính nhất quán
- **Live trading:** Chỉ áp dụng khi `live_trading=true` trong config
- **Demo mode:** Trong demo mode, bot chỉ log thông tin mà không thực sự đóng lệnh

## Cách kiểm tra

### 1. Test với config khác nhau:

```bash
python test_config_based_logic.py
```

### 2. Test unit test:

```bash
python -m pytest tests/test_risk_manager.py::TestRiskManager::test_should_close_position_max_age -v
```

### 3. Test thay đổi config:

Thay đổi giá trị `max_position_age_minutes` trong `config/config.yaml` và chạy bot để kiểm tra:

```yaml
risk:
  max_position_age_minutes: 15  # Thay đổi thành 15 phút
```

## Cấu hình mới

### Thêm vào file `config/config.yaml`:

```yaml
risk:
  # ... các cấu hình khác ...
  max_position_age_minutes: 25  # Thời gian tối đa giữ lệnh (phút)
```

### Tùy chỉnh thời gian:
- **Mặc định**: 25 phút
- **Tùy chỉnh**: Có thể thay đổi thành bất kỳ giá trị nào (ví dụ: 15, 30, 60 phút)
- **Áp dụng**: Cho cả live trading và backtesting

### Các cấu hình khác vẫn hoạt động bình thường:
- `max_positions`: Số lệnh tối đa cùng lúc
- `risk_per_trade`: Phần trăm rủi ro mỗi lệnh
- `timeframe`: Chu kỳ kiểm tra (5 phút)
