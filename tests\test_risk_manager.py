"""
Unit tests for Risk Manager
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from src.risk.risk_manager import RiskManager, PositionSize, RiskMetrics
from src.utils.config import RiskConfig, TradingConfig
from src.core.mt5_client import PositionInfo


class TestRiskManager:
    """Test cases for RiskManager class"""

    def setup_method(self):
        """Setup test fixtures"""
        self.risk_config = RiskConfig(
            max_drawdown=0.10,
            stop_loss_atr_multiplier=2.0,
            take_profit_atr_multiplier=3.0,
            trailing_stop=True,
            trailing_stop_distance=50,
            max_spread=30,
            max_position_age_minutes=25
        )

        self.trading_config = TradingConfig(
            timeframe=5,
            max_positions=3,
            max_daily_trades=10,
            risk_per_trade=0.02,
            min_volume=0.01,
            max_volume=1.0,
            volume_step=0.01
        )

        self.risk_manager = RiskManager(self.risk_config, self.trading_config)

    def test_calculate_position_size_basic(self):
        """Test basic position size calculation"""
        account_balance = 10000.0
        entry_price = 2000.0
        stop_loss = 1980.0
        atr_value = 15.0

        symbol_info = {
            'contract_size': 100,
            'point': 0.00001,
            'volume_min': 0.01,
            'volume_max': 1.0,
            'volume_step': 0.01
        }

        result = self.risk_manager.calculate_position_size(
            account_balance, entry_price, stop_loss, atr_value, symbol_info
        )

        assert isinstance(result, PositionSize)
        assert result.volume >= symbol_info['volume_min']
        assert result.volume <= symbol_info['volume_max']
        assert result.risk_amount == account_balance * self.trading_config.risk_per_trade
        assert result.stop_loss == stop_loss
        assert result.risk_reward_ratio > 0

    def test_calculate_position_size_with_atr_stop_loss(self):
        """Test position size calculation when stop loss is calculated from ATR"""
        account_balance = 10000.0
        entry_price = 2000.0
        stop_loss = 0.0  # No stop loss provided
        atr_value = 15.0

        symbol_info = {
            'contract_size': 100,
            'point': 0.00001,
            'volume_min': 0.01,
            'volume_max': 1.0,
            'volume_step': 0.01
        }

        result = self.risk_manager.calculate_position_size(
            account_balance, entry_price, stop_loss, atr_value, symbol_info
        )

        # Stop loss should be calculated from ATR
        expected_sl = entry_price - (atr_value * self.risk_config.stop_loss_atr_multiplier)
        assert abs(result.stop_loss - expected_sl) < 0.01

        # Take profit should be calculated from ATR
        expected_tp = entry_price + (atr_value * self.risk_config.take_profit_atr_multiplier)
        assert abs(result.take_profit - expected_tp) < 0.01

    def test_check_trading_allowed_max_positions(self):
        """Test trading allowed check with maximum positions"""
        # Create mock positions
        positions = [Mock() for _ in range(self.trading_config.max_positions)]
        account_info = {'balance': 10000, 'equity': 10000, 'margin_level': 500}

        allowed, reason = self.risk_manager.check_trading_allowed(positions, account_info)

        assert not allowed
        assert "Maximum positions limit reached" in reason

    def test_check_trading_allowed_daily_trades_limit(self):
        """Test trading allowed check with daily trades limit"""
        # Set up daily trades to maximum
        today = datetime.now().date()
        self.risk_manager.daily_trades[today] = self.trading_config.max_daily_trades

        positions = []
        account_info = {'balance': 10000, 'equity': 10000, 'margin_level': 500}

        allowed, reason = self.risk_manager.check_trading_allowed(positions, account_info)

        assert not allowed
        assert "Daily trades limit reached" in reason

    def test_check_trading_allowed_max_drawdown(self):
        """Test trading allowed check with maximum drawdown"""
        # Set peak balance higher to simulate drawdown
        self.risk_manager.peak_balance = 12000

        positions = []
        account_info = {'balance': 10000, 'equity': 9000, 'margin_level': 500}  # 25% drawdown

        allowed, reason = self.risk_manager.check_trading_allowed(positions, account_info)

        assert not allowed
        assert "Maximum drawdown exceeded" in reason

    def test_check_trading_allowed_low_margin(self):
        """Test trading allowed check with low margin level"""
        positions = []
        account_info = {'balance': 10000, 'equity': 10000, 'margin_level': 150}  # Below 200%

        allowed, reason = self.risk_manager.check_trading_allowed(positions, account_info)

        assert not allowed
        assert "Insufficient margin level" in reason

    def test_check_trading_allowed_success(self):
        """Test successful trading allowed check"""
        positions = []
        account_info = {'balance': 10000, 'equity': 10000, 'margin_level': 500}

        allowed, reason = self.risk_manager.check_trading_allowed(positions, account_info)

        assert allowed
        assert reason == "Trading allowed"

    def test_check_spread_condition(self):
        """Test spread condition check"""
        # Good spread
        symbol_info = {'spread': 20}
        assert self.risk_manager.check_spread_condition(symbol_info)

        # Bad spread
        symbol_info = {'spread': 50}
        assert not self.risk_manager.check_spread_condition(symbol_info)

    def test_calculate_current_drawdown(self):
        """Test current drawdown calculation"""
        self.risk_manager.peak_balance = 12000

        account_info = {'equity': 10000}
        drawdown = self.risk_manager.calculate_current_drawdown(account_info)

        expected_drawdown = (12000 - 10000) / 12000
        assert abs(drawdown - expected_drawdown) < 0.001

    def test_calculate_current_drawdown_no_peak(self):
        """Test current drawdown calculation with no peak balance"""
        self.risk_manager.peak_balance = 0

        account_info = {'equity': 10000}
        drawdown = self.risk_manager.calculate_current_drawdown(account_info)

        assert drawdown == 0.0

    def test_should_close_position_trailing_stop(self):
        """Test position closure due to trailing stop"""
        position = PositionInfo(
            ticket=123,
            symbol="XAUUSD",
            type=0,  # Buy order
            volume=0.1,
            price_open=2000.0,
            price_current=2010.0,
            profit=100.0,
            swap=0.0,
            commission=0.0,
            time=datetime.now() - timedelta(minutes=30)
        )

        current_price = 1995.0  # Price dropped significantly
        atr_value = 15.0

        should_close, reason = self.risk_manager.should_close_position(
            position, current_price, atr_value
        )

        # Should close due to trailing stop
        assert should_close
        assert "trailing stop" in reason.lower()

    def test_should_close_position_max_age(self):
        """Test position closure due to maximum age"""
        position = PositionInfo(
            ticket=123,
            symbol="XAUUSD",
            type=0,
            volume=0.1,
            price_open=2000.0,
            price_current=2010.0,
            profit=100.0,
            swap=0.0,
            commission=0.0,
            time=datetime.now() - timedelta(minutes=26)  # Older than 25 minutes
        )

        current_price = 2010.0
        atr_value = 15.0

        should_close, reason = self.risk_manager.should_close_position(
            position, current_price, atr_value
        )

        assert should_close
        assert "age" in reason.lower()

    def test_should_close_position_underwater(self):
        """Test position closure when significantly underwater"""
        position = PositionInfo(
            ticket=123,
            symbol="XAUUSD",
            type=0,
            volume=0.1,
            price_open=2000.0,
            price_current=1900.0,  # 5% loss
            profit=-1000.0,  # Significant loss
            swap=0.0,
            commission=0.0,
            time=datetime.now() - timedelta(minutes=30)
        )

        current_price = 1900.0
        atr_value = 15.0

        should_close, reason = self.risk_manager.should_close_position(
            position, current_price, atr_value
        )

        assert should_close
        assert "underwater" in reason.lower()

    def test_should_close_position_keep_open(self):
        """Test position that should remain open"""
        position = PositionInfo(
            ticket=123,
            symbol="XAUUSD",
            type=0,
            volume=0.1,
            price_open=2000.0,
            price_current=2010.0,
            profit=100.0,
            swap=0.0,
            commission=0.0,
            time=datetime.now() - timedelta(minutes=30)
        )

        current_price = 2010.0
        atr_value = 15.0

        should_close, reason = self.risk_manager.should_close_position(
            position, current_price, atr_value
        )

        assert not should_close
        assert "within risk parameters" in reason.lower()

    def test_update_daily_trades(self):
        """Test daily trades counter update"""
        initial_count = self.risk_manager.daily_trades.get(datetime.now().date(), 0)

        self.risk_manager.update_daily_trades()

        new_count = self.risk_manager.daily_trades.get(datetime.now().date(), 0)
        assert new_count == initial_count + 1

    def test_add_trade_to_history(self):
        """Test adding trade to history"""
        trade_data = {
            'symbol': 'XAUUSD',
            'action': 'buy',
            'volume': 0.1,
            'profit': 100.0,
            'entry_price': 2000.0,
            'exit_price': 2010.0
        }

        initial_count = len(self.risk_manager.trade_history)
        self.risk_manager.add_trade_to_history(trade_data)

        assert len(self.risk_manager.trade_history) == initial_count + 1
        assert self.risk_manager.trade_history[-1]['symbol'] == 'XAUUSD'
        assert 'timestamp' in self.risk_manager.trade_history[-1]

    def test_calculate_risk_metrics_no_trades(self):
        """Test risk metrics calculation with no trades"""
        account_info = {'balance': 10000, 'equity': 10000}

        metrics = self.risk_manager.calculate_risk_metrics(account_info)

        assert isinstance(metrics, RiskMetrics)
        assert metrics.win_rate == 0
        assert metrics.profit_factor == 0
        assert metrics.total_risk_exposure == 0

    def test_calculate_risk_metrics_with_trades(self):
        """Test risk metrics calculation with trade history"""
        # Add some mock trades
        trades = [
            {'profit': 100, 'timestamp': datetime.now()},
            {'profit': -50, 'timestamp': datetime.now()},
            {'profit': 75, 'timestamp': datetime.now()},
            {'profit': -25, 'timestamp': datetime.now()},
        ]

        for trade in trades:
            self.risk_manager.add_trade_to_history(trade)

        account_info = {'balance': 10000, 'equity': 10000}
        metrics = self.risk_manager.calculate_risk_metrics(account_info)

        assert metrics.win_rate == 0.5  # 2 wins out of 4 trades
        assert metrics.profit_factor > 0
        assert metrics.sharpe_ratio != 0
        assert metrics.var_95 <= 0  # VaR should be negative or zero

    def test_get_risk_summary(self):
        """Test risk summary generation"""
        positions = []
        account_info = {
            'balance': 10000,
            'equity': 10000,
            'margin_level': 500
        }

        summary = self.risk_manager.get_risk_summary(account_info, positions)

        assert isinstance(summary, dict)
        assert 'current_drawdown' in summary
        assert 'account_balance' in summary
        assert 'win_rate' in summary
        assert 'open_positions' in summary

        # Check format of values
        assert summary['account_balance'].startswith('$')
        assert summary['current_drawdown'].endswith('%')


if __name__ == '__main__':
    pytest.main([__file__])
