# Hướng Dẫn Backtest - MT5 Trading Bot

## 🎯 Tổng Quan

<PERSON> 3 cách để chạy backtest với bot này:

1. **Quick Backtest Tool** (Khu<PERSON><PERSON><PERSON> nghị) - <PERSON><PERSON> dàng và linh hoạt nhất
2. **Config File** - Chỉnh sửa config.yaml
3. **Standard Backtest** - Sử dụng run_backtest.py truyền thống

---

## 🚀 Cách 1: Quick Backtest Tool (Khuyến nghị)

### Cài đặt và sử dụng

```bash
# Xem các preset có sẵn
python quick_backtest.py --list-presets

# Backtest tháng trước
python quick_backtest.py --preset last_month

# Backtest tuần trước
python quick_backtest.py --preset last_week

# Backtest quý 2 năm 2024
python quick_backtest.py --preset q2_2024

# Backtest khoảng thời gian cụ thể
python quick_backtest.py --start 2024-06-01 --end 2024-07-01

# Backtest với số dư khác
python quick_backtest.py --preset last_month --balance 1000
```

### Các preset có sẵn:

| Preset | Mô tả | Thời gian |
|--------|-------|-----------|
| `last_week` | Tuần trước | 7 ngày gần nhất |
| `last_month` | Tháng trước | 30 ngày gần nhất |
| `last_3months` | 3 tháng trước | 90 ngày gần nhất |
| `last_6months` | 6 tháng trước | 180 ngày gần nhất |
| `last_year` | Năm trước | 365 ngày gần nhất |
| `ytd` | Từ đầu năm | 01/01 đến hiện tại |
| `q1_2024` | Quý 1/2024 | 01/01 - 31/03/2024 |
| `q2_2024` | Quý 2/2024 | 01/04 - 30/06/2024 |
| `q3_2024` | Quý 3/2024 | 01/07 - 30/09/2024 |
| `q4_2024` | Quý 4/2024 | 01/10 - 31/12/2024 |

### Ưu điểm:
- ✅ Không cần chỉnh sửa file config
- ✅ Hỗ trợ nhiều format ngày tháng
- ✅ Có preset sẵn cho các khoảng thời gian phổ biến
- ✅ Tự động validate và điều chỉnh ngày tháng
- ✅ Có thể thay đổi số dư ban đầu

---

## ⚙️ Cách 2: Config File

### Chỉnh sửa config.yaml

Mở file `config/config.yaml` và chỉnh sửa phần backtest:

```yaml
backtest:
  # Thời gian backtest - Dễ dàng thay đổi để test các khoảng thời gian khác nhau
  start_date: '2024-06-01'    # Ngày bắt đầu backtest (YYYY-MM-DD)
  end_date: '2024-07-01'      # Ngày kết thúc backtest (YYYY-MM-DD)
  
  # Cấu hình tài chính
  initial_balance: 1000.0     # Số dư ban đầu
  commission: 0.03            # Phí giao dịch
  spread: 200                 # Spread trung bình (points)
```

### Chạy backtest:

```bash
python run_backtest.py
```

### Ưu điểm:
- ✅ Cấu hình được lưu lại
- ✅ Có thể cấu hình chi tiết các thông số khác
- ✅ Phù hợp cho backtest lặp lại nhiều lần

### Nhược điểm:
- ❌ Phải chỉnh sửa file mỗi lần thay đổi
- ❌ Dễ quên restore lại cấu hình cũ

---

## 📊 Cách 3: Standard Backtest

Sử dụng file `run_backtest.py` truyền thống. File này sẽ:

1. Đọc cấu hình từ config.yaml
2. Nếu có `start_date` và `end_date` trong config → sử dụng
3. Nếu không → sử dụng 60 ngày gần nhất

```bash
python run_backtest.py
```

---

## 📈 Kết Quả Backtest

Tất cả các phương pháp đều tạo ra:

### 1. Kết quả trên console:
- Tổng quan hiệu suất
- Số liệu thống kê chi tiết
- Đánh giá và khuyến nghị

### 2. File báo cáo HTML:
- `reports/backtest_report_YYYYMMDD_HHMMSS.html`
- Báo cáo chi tiết với biểu đồ

### 3. Biểu đồ phân tích:
- `reports/equity_curve.png` - Đường cong vốn
- `reports/drawdown.png` - Biểu đồ drawdown
- `reports/trade_analysis.png` - Phân tích giao dịch
- `reports/monthly_returns.png` - Lợi nhuận theo tháng
- Và nhiều biểu đồ khác...

---

## 💡 Mẹo Sử Dụng

### 1. Chọn khoảng thời gian phù hợp:
- **Test nhanh**: 1-2 tuần (`last_week`)
- **Test trung bình**: 1-3 tháng (`last_month`, `last_3months`)
- **Test toàn diện**: 6-12 tháng (`last_6months`, `last_year`)

### 2. So sánh các khoảng thời gian:
```bash
# Test nhiều khoảng thời gian khác nhau
python quick_backtest.py --preset q1_2024
python quick_backtest.py --preset q2_2024
python quick_backtest.py --preset q3_2024
```

### 3. Test với số dư khác nhau:
```bash
# Test với số dư nhỏ
python quick_backtest.py --preset last_month --balance 100

# Test với số dư lớn
python quick_backtest.py --preset last_month --balance 10000
```

### 4. Format ngày tháng được hỗ trợ:
- `2024-06-01` (YYYY-MM-DD)
- `01/06/2024` (DD/MM/YYYY)
- `01-06-2024` (DD-MM-YYYY)
- `2024/06/01` (YYYY/MM/DD)

---

## ⚠️ Lưu Ý Quan Trọng

1. **Dữ liệu**: Bot cần kết nối MT5 để lấy dữ liệu lịch sử
2. **Thời gian**: Backtest cần ít nhất 30 ngày dữ liệu trước start_date để tính toán indicators
3. **Kết quả**: Backtest chỉ là tham khảo, không đảm bảo kết quả tương lai
4. **Demo**: Luôn test trên demo trước khi live trading

---

## 🔧 Troubleshooting

### Lỗi "Insufficient data":
```bash
# Tăng thời gian thu thập dữ liệu
python quick_backtest.py --start 2024-05-01 --end 2024-07-01
```

### Lỗi kết nối MT5:
- Kiểm tra MT5 đang chạy
- Kiểm tra thông tin đăng nhập trong config.yaml
- Kiểm tra kết nối internet

### Lỗi parse ngày:
```bash
# Sử dụng format đúng
python quick_backtest.py --start 2024-06-01 --end 2024-07-01
```

---

## 📞 Hỗ Trợ

Nếu gặp vấn đề, hãy:
1. Kiểm tra log trong console
2. Xem file log trong thư mục logs/
3. Đảm bảo config.yaml được cấu hình đúng
4. Kiểm tra kết nối MT5

**Chúc bạn backtest thành công! 🚀**
