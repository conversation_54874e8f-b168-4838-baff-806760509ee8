"""
Backtesting Engine for MT5 Trading Bot
Comprehensive backtesting framework with performance metrics and visualization
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json

from ..strategy.gold_strategy import GoldTradingStrategy, TradingSignal, MarketState
from ..indicators.technical_indicators import TechnicalIndicators
from ..risk.risk_manager import RiskManager
from ..utils.logger import get_logger
from ..utils.config import BotConfig, BacktestConfig

logger = get_logger(__name__)


@dataclass
class Trade:
    """Individual trade record"""
    entry_time: datetime
    exit_time: datetime
    symbol: str
    direction: str  # 'long' or 'short'
    entry_price: float
    exit_price: float
    volume: float
    pnl: float
    pnl_pct: float
    commission: float
    swap: float
    duration_minutes: int
    entry_reason: str
    exit_reason: str
    stop_loss_price: float = 0.0  # Stop Loss price
    take_profit_price: float = 0.0  # Take Profit price
    max_favorable_excursion: float = 0.0
    max_adverse_excursion: float = 0.0


@dataclass
class BacktestResults:
    """Backtest results summary"""
    start_date: datetime
    end_date: datetime
    initial_balance: float
    final_balance: float
    total_return: float
    total_return_pct: float
    max_drawdown: float
    max_drawdown_pct: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    profit_factor: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    max_consecutive_wins: int
    max_consecutive_losses: int
    avg_trade_duration: float
    # Volume statistics
    total_volume: float
    avg_volume_per_trade: float
    max_volume_per_trade: float
    min_volume_per_trade: float
    volume_std: float
    trades: List[Trade]
    equity_curve: pd.Series
    drawdown_curve: pd.Series


class BacktestEngine:
    """Main backtesting engine"""

    def __init__(self, config: BotConfig):
        self.config = config
        self.backtest_config = config.backtest

        # Initialize components
        self.indicators = TechnicalIndicators(config)
        self.risk_manager = RiskManager(config.risk, config.trading)

        # Backtest state
        self.current_time = None
        self.balance = self.backtest_config.initial_balance
        self.equity = self.backtest_config.initial_balance
        self.peak_equity = self.backtest_config.initial_balance
        self.open_positions = []
        self.closed_trades = []
        self.equity_history = []
        self.drawdown_history = []

        # Performance tracking
        self.daily_returns = []
        self.trade_count = 0

    def run_backtest(self, data: pd.DataFrame,
                    start_date: Optional[datetime] = None,
                    end_date: Optional[datetime] = None) -> BacktestResults:
        """
        Run comprehensive backtest

        Args:
            data: Historical market data
            start_date: Backtest start date
            end_date: Backtest end date

        Returns:
            BacktestResults object with complete analysis
        """
        logger.info("Starting backtest...")

        # Filter data by date range
        if start_date:
            data = data[data.index >= start_date]
        if end_date:
            data = data[data.index <= end_date]

        if data.empty:
            raise ValueError("No data available for backtesting")

        # Initialize backtest
        self._initialize_backtest(data.index[0], data.index[-1])

        # Main backtest loop
        for i in range(len(data)):
            current_bar = data.iloc[i:i+1]
            historical_data = data.iloc[:i+1]

            if len(historical_data) < 50:  # Need enough data for indicators
                continue

            self.current_time = current_bar.index[0]
            current_price = current_bar['close'].iloc[0]

            # Update open positions
            self._update_positions(current_bar)

            # Generate trading signal
            signal = self._generate_signal(historical_data, current_bar)

            # Execute signal
            if signal:
                self._execute_signal(signal, current_bar)

            # Update equity and drawdown
            self._update_equity(current_price)

            # Log progress
            if i % 1000 == 0:
                logger.info(f"Processed {i}/{len(data)} bars ({i/len(data)*100:.1f}%)")

        # Close remaining positions
        self._close_all_positions(data.iloc[-1])

        # Calculate final results
        results = self._calculate_results()

        logger.info(f"Backtest completed: {results.total_trades} trades, "
                   f"{results.win_rate:.1f}% win rate, "
                   f"{results.total_return_pct:.2f}% return")

        return results

    def _initialize_backtest(self, start_date: datetime, end_date: datetime):
        """Initialize backtest state"""
        self.balance = self.backtest_config.initial_balance
        self.equity = self.backtest_config.initial_balance
        self.peak_equity = self.backtest_config.initial_balance
        self.open_positions = []
        self.closed_trades = []
        self.equity_history = []
        self.drawdown_history = []
        self.daily_returns = []
        self.trade_count = 0

        logger.info(f"Backtest initialized: {start_date} to {end_date}")
        logger.info(f"Initial balance: ${self.balance:.2f}")

    def _generate_signal(self, historical_data: pd.DataFrame,
                        current_bar: pd.DataFrame) -> Optional[TradingSignal]:
        """Generate trading signal for current bar"""
        try:
            # Calculate indicators with config parameters
            macd_signal = self.indicators.calculate_macd(
                historical_data,
                fast_period=self.config.indicators.macd_fast,
                slow_period=self.config.indicators.macd_slow,
                signal_period=self.config.indicators.macd_signal
            )
            atr_data = self.indicators.calculate_atr(
                historical_data,
                period=self.config.indicators.atr_period
            )
            pivot_points = self.indicators.calculate_pivot_points(historical_data)

            # Create market state
            current_price = current_bar['close'].iloc[0]
            # Calculate spread from config
            spread = self.backtest_config.spread / 10000  # Convert points to price
            market_state = MarketState(
                price=current_price,
                bid=current_price - spread/2,  # Simulate spread
                ask=current_price + spread/2,
                spread=spread,
                macd_signal=macd_signal,
                atr_data=atr_data,
                pivot_points=pivot_points,
                ai_prediction=None,
                ai_confidence=0.0,
                market_trend=self._determine_trend(historical_data),
                volatility=atr_data.volatility_level
            )

            # Create mock account info for risk management
            account_info = {
                'balance': self.balance,
                'equity': self.equity,
                'margin_level': 500,  # Mock margin level
                'leverage': 500  # Mock leverage
            }

            # Create mock positions for risk management
            mock_positions = []
            for pos in self.open_positions:
                from ..core.mt5_client import PositionInfo
                mock_position = PositionInfo(
                    ticket=len(mock_positions),
                    symbol=self.config.mt5.symbol,
                    type=1 if pos['direction'] == 'long' else 0,
                    volume=pos['volume'],
                    price_open=pos['entry_price'],
                    price_current=current_price,
                    profit=0,  # Will be calculated
                    swap=0,
                    commission=self.backtest_config.commission,  # Commission from config
                    time=pos['entry_time']
                )
                mock_positions.append(mock_position)

            # Check if trading is allowed using risk manager
            trading_allowed, reason = self.risk_manager.check_trading_allowed(
                mock_positions, account_info
            )

            if not trading_allowed:
                logger.debug(f"Trading not allowed in backtest: {reason}")
                return None

            # Generate signal using strategy logic
            signal = self._evaluate_strategy_conditions(market_state)

            return signal

        except Exception as e:
            logger.error(f"Error generating signal: {e}")
            return None

    def _evaluate_strategy_conditions(self, market_state: MarketState) -> Optional[TradingSignal]:
        """Evaluate strategy conditions for signal generation"""
        # Check if we can open new positions
        if len(self.open_positions) >= self.config.trading.max_positions:
            return None

        # Calculate technical score using same logic as real bot
        buy_score = 0
        sell_score = 0
        factors = []

        # MACD analysis
        if market_state.macd_signal.crossover == 'bullish_cross':
            buy_score += self.config.strategy_scoring.macd_crossover_bullish
            factors.append('MACD bullish crossover')
        elif market_state.macd_signal.crossover == 'bearish_cross':
            sell_score += self.config.strategy_scoring.macd_crossover_bearish
            factors.append('MACD bearish crossover')
        elif market_state.macd_signal.trend == 'bullish':
            buy_score += self.config.strategy_scoring.macd_trend_bullish
            factors.append('MACD bullish trend')
        elif market_state.macd_signal.trend == 'bearish':
            sell_score += self.config.strategy_scoring.macd_trend_bearish
            factors.append('MACD bearish trend')

        # Pivot Points analysis
        if market_state.pivot_points.current_level == 'above_pivot':
            buy_score += self.config.strategy_scoring.pivot_above
            factors.append('Price above pivot')
        elif market_state.pivot_points.current_level == 'below_pivot':
            sell_score += self.config.strategy_scoring.pivot_below
            factors.append('Price below pivot')

        # Market trend
        if market_state.market_trend == 'bullish':
            buy_score += self.config.strategy_scoring.market_trend_bullish
            factors.append('Bullish trend')
        elif market_state.market_trend == 'bearish':
            sell_score += self.config.strategy_scoring.market_trend_bearish
            factors.append('Bearish trend')

        # Apply technical weight (same as real bot)
        buy_score *= self.config.strategy_scoring.technical_weight
        sell_score *= self.config.strategy_scoring.technical_weight

        # Generate signal if score is high enough
        min_score = self.config.strategy_scoring.min_signal_strength

        if buy_score > min_score:
            return self._create_buy_signal(market_state, buy_score, factors)
        elif sell_score > min_score:
            return self._create_sell_signal(market_state, sell_score, factors)

        return None

    def _create_buy_signal(self, market_state: MarketState,
                          score: float, factors: List[str]) -> TradingSignal:
        """Create buy signal"""
        entry_price = market_state.ask
        atr = market_state.atr_data.atr

        stop_loss = entry_price - (atr * self.config.risk.stop_loss_atr_multiplier)
        take_profit = entry_price + (atr * self.config.risk.take_profit_atr_multiplier)

        # Calculate position size
        risk_amount = self.balance * self.config.trading.risk_per_trade
        sl_distance = entry_price - stop_loss
        volume = min(risk_amount / (sl_distance * 100), self.config.trading.max_volume)
        volume = max(volume, self.config.trading.min_volume)

        return TradingSignal(
            action='buy',
            strength=score,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            volume=volume,
            confidence=score,
            reasoning="BUY: " + " | ".join(factors),
            timestamp=self.current_time
        )

    def _create_sell_signal(self, market_state: MarketState,
                           score: float, factors: List[str]) -> TradingSignal:
        """Create sell signal"""
        entry_price = market_state.bid
        atr = market_state.atr_data.atr

        stop_loss = entry_price + (atr * self.config.risk.stop_loss_atr_multiplier)
        take_profit = entry_price - (atr * self.config.risk.take_profit_atr_multiplier)

        # Calculate position size
        risk_amount = self.balance * self.config.trading.risk_per_trade
        sl_distance = stop_loss - entry_price
        volume = min(risk_amount / (sl_distance * 100), self.config.trading.max_volume)
        volume = max(volume, self.config.trading.min_volume)

        return TradingSignal(
            action='sell',
            strength=score,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            volume=volume,
            confidence=score,
            reasoning="SELL: " + " | ".join(factors),
            timestamp=self.current_time
        )

    def _execute_signal(self, signal: TradingSignal, current_bar: pd.DataFrame):
        """Execute trading signal"""
        if signal.action in ['buy', 'sell']:
            # Create new position
            position = {
                'entry_time': self.current_time,
                'symbol': self.config.mt5.symbol,
                'direction': 'long' if signal.action == 'buy' else 'short',
                'entry_price': signal.entry_price,
                'volume': signal.volume,
                'stop_loss': signal.stop_loss,
                'take_profit': signal.take_profit,
                'stop_loss_price': signal.stop_loss,
                'take_profit_price': signal.take_profit,
                'entry_reason': signal.reasoning,
                'max_favorable': 0.0,
                'max_adverse': 0.0
            }

            self.open_positions.append(position)
            self.trade_count += 1

            # Deduct commission
            commission = self.backtest_config.commission
            self.balance -= commission

    def _update_positions(self, current_bar: pd.DataFrame):
        """Update open positions and check for exits"""
        current_price = current_bar['close'].iloc[0]
        high_price = current_bar['high'].iloc[0]
        low_price = current_bar['low'].iloc[0]

        positions_to_close = []

        for i, position in enumerate(self.open_positions):
            # Check maximum position age
            position_age = self.current_time - position['entry_time']
            max_age_minutes = self.config.risk.max_position_age_minutes
            if position_age.total_seconds() > max_age_minutes * 60:  # Convert minutes to seconds
                positions_to_close.append((i, current_price, f'Maximum position age exceeded ({max_age_minutes} minutes)'))
                continue

            # Update max favorable/adverse excursion
            if position['direction'] == 'long':
                favorable = high_price - position['entry_price']
                adverse = position['entry_price'] - low_price

                # Check stop loss and take profit
                if low_price <= position['stop_loss']:
                    positions_to_close.append((i, position['stop_loss'], 'Stop Loss'))
                elif high_price >= position['take_profit']:
                    positions_to_close.append((i, position['take_profit'], 'Take Profit'))
            else:  # short
                favorable = position['entry_price'] - low_price
                adverse = high_price - position['entry_price']

                # Check stop loss and take profit
                if high_price >= position['stop_loss']:
                    positions_to_close.append((i, position['stop_loss'], 'Stop Loss'))
                elif low_price <= position['take_profit']:
                    positions_to_close.append((i, position['take_profit'], 'Take Profit'))

            # Update excursions
            position['max_favorable'] = max(position['max_favorable'], favorable)
            position['max_adverse'] = max(position['max_adverse'], adverse)

        # Close positions
        for i, exit_price, exit_reason in reversed(positions_to_close):
            self._close_position(i, exit_price, exit_reason)

    def _close_position(self, position_index: int, exit_price: float, exit_reason: str):
        """Close a specific position"""
        position = self.open_positions[position_index]

        # Calculate P&L
        if position['direction'] == 'long':
            pnl = (exit_price - position['entry_price']) * position['volume'] * 100
        else:
            pnl = (position['entry_price'] - exit_price) * position['volume'] * 100

        pnl_pct = pnl / (position['entry_price'] * position['volume'] * 100)

        # Create trade record
        duration = (self.current_time - position['entry_time']).total_seconds() / 60

        trade = Trade(
            entry_time=position['entry_time'],
            exit_time=self.current_time,
            symbol=position['symbol'],
            direction=position['direction'],
            entry_price=position['entry_price'],
            exit_price=exit_price,
            volume=position['volume'],
            pnl=pnl,
            pnl_pct=pnl_pct,
            commission=self.backtest_config.commission * 2,  # Entry + Exit
            swap=0.0,
            duration_minutes=int(duration),
            entry_reason=position['entry_reason'],
            exit_reason=exit_reason,
            stop_loss_price=position.get('stop_loss_price', 0.0),
            take_profit_price=position.get('take_profit_price', 0.0),
            max_favorable_excursion=position['max_favorable'],
            max_adverse_excursion=position['max_adverse']
        )

        self.closed_trades.append(trade)

        # Update balance
        self.balance += pnl - trade.commission

        # Remove position
        del self.open_positions[position_index]

    def _close_all_positions(self, final_bar: pd.DataFrame):
        """Close all remaining positions at the end of backtest"""
        # Support both DataFrame and Series/float for final_bar
        if isinstance(final_bar, pd.DataFrame):
            if 'close' in final_bar.columns:
                # If DataFrame, get the first row's close
                final_price = final_bar['close'].iloc[0]
            else:
                # fallback: try to use as Series
                final_price = final_bar.iloc[0] if hasattr(final_bar, 'iloc') else float(final_bar)
        elif hasattr(final_bar, 'iloc'):
            final_price = final_bar.iloc[0]
        else:
            final_price = float(final_bar)

        while self.open_positions:
            self._close_position(0, final_price, 'End of Backtest')

    def _update_equity(self, current_price: float):
        """Update equity and drawdown calculations"""
        # Calculate unrealized P&L
        unrealized_pnl = 0
        for position in self.open_positions:
            if position['direction'] == 'long':
                unrealized_pnl += (current_price - position['entry_price']) * position['volume'] * 100
            else:
                unrealized_pnl += (position['entry_price'] - current_price) * position['volume'] * 100

        self.equity = self.balance + unrealized_pnl

        # Update peak equity and drawdown
        if self.equity > self.peak_equity:
            self.peak_equity = self.equity

        drawdown = (self.peak_equity - self.equity) / self.peak_equity

        # Store history
        self.equity_history.append({
            'time': self.current_time,
            'equity': self.equity,
            'balance': self.balance,
            'drawdown': drawdown
        })

    def _determine_trend(self, data: pd.DataFrame) -> str:
        """Determine market trend from price data"""
        if len(data) < 20:
            return 'sideways'

        recent_prices = data['close'].tail(20)
        if len(recent_prices) >= 2:
            if recent_prices.iloc[-1] > recent_prices.iloc[0]:
                return 'bullish'
            elif recent_prices.iloc[-1] < recent_prices.iloc[0]:
                return 'bearish'
            else:
                return 'sideways'
        else:
            return 'sideways'

    def _calculate_results(self) -> BacktestResults:
        """Calculate comprehensive backtest results"""
        if not self.closed_trades:
            raise ValueError("No trades executed during backtest")

        # Basic metrics
        total_trades = len(self.closed_trades)
        winning_trades = sum(1 for trade in self.closed_trades if trade.pnl > 0)
        losing_trades = total_trades - winning_trades

        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        # P&L metrics
        total_pnl = sum(trade.pnl for trade in self.closed_trades)
        total_return_pct = (self.balance - self.backtest_config.initial_balance) / self.backtest_config.initial_balance

        # Win/Loss analysis
        winning_pnls = [trade.pnl for trade in self.closed_trades if trade.pnl > 0]
        losing_pnls = [trade.pnl for trade in self.closed_trades if trade.pnl < 0]

        avg_win = np.mean(winning_pnls) if winning_pnls else 0
        avg_loss = np.mean(losing_pnls) if losing_pnls else 0

        profit_factor = abs(sum(winning_pnls) / sum(losing_pnls)) if losing_pnls else float('inf')

        # Drawdown analysis
        equity_series = pd.Series(
            [h['equity'] for h in self.equity_history],
            index=[h['time'] for h in self.equity_history]
        )
        drawdown_series = pd.Series(
            [h['drawdown'] for h in self.equity_history],
            index=[h['time'] for h in self.equity_history]
        )

        max_drawdown = drawdown_series.max()
        max_drawdown_pct = max_drawdown

        # Risk metrics
        returns = equity_series.pct_change().dropna()
        sharpe_ratio = self._calculate_sharpe_ratio(returns)
        sortino_ratio = self._calculate_sortino_ratio(returns)
        calmar_ratio = total_return_pct / max_drawdown_pct if max_drawdown_pct > 0 else 0

        # Consecutive wins/losses
        max_consecutive_wins, max_consecutive_losses = self._calculate_consecutive_trades()

        # Average trade duration
        avg_duration = np.mean([trade.duration_minutes for trade in self.closed_trades])

        # Volume statistics
        volumes = [trade.volume for trade in self.closed_trades]
        total_volume = sum(volumes)
        avg_volume_per_trade = np.mean(volumes) if volumes else 0
        max_volume_per_trade = max(volumes) if volumes else 0
        min_volume_per_trade = min(volumes) if volumes else 0
        volume_std = np.std(volumes) if volumes else 0

        return BacktestResults(
            start_date=self.equity_history[0]['time'] if self.equity_history else datetime.now(),
            end_date=self.equity_history[-1]['time'] if self.equity_history else datetime.now(),
            initial_balance=self.backtest_config.initial_balance,
            final_balance=self.balance,
            total_return=total_pnl,
            total_return_pct=total_return_pct,
            max_drawdown=max_drawdown * self.backtest_config.initial_balance,
            max_drawdown_pct=max_drawdown_pct,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            avg_win=float(avg_win),
            avg_loss=float(avg_loss),
            profit_factor=profit_factor,
            sharpe_ratio=sharpe_ratio,
            sortino_ratio=sortino_ratio,
            calmar_ratio=calmar_ratio,
            max_consecutive_wins=max_consecutive_wins,
            max_consecutive_losses=max_consecutive_losses,
            avg_trade_duration=float(avg_duration),
            total_volume=float(total_volume),
            avg_volume_per_trade=float(avg_volume_per_trade),
            max_volume_per_trade=float(max_volume_per_trade),
            min_volume_per_trade=float(min_volume_per_trade),
            volume_std=float(volume_std),
            trades=self.closed_trades,
            equity_curve=equity_series,
            drawdown_curve=drawdown_series
        )

    def _calculate_sharpe_ratio(self, returns: pd.Series) -> float:
        """Calculate Sharpe ratio"""
        if returns.std() == 0:
            return 0
        return returns.mean() / returns.std() * np.sqrt(252 * 24 * 12)  # Annualized for 5-min data

    def _calculate_sortino_ratio(self, returns: pd.Series) -> float:
        """Calculate Sortino ratio"""
        negative_returns = returns[returns < 0]
        if len(negative_returns) == 0 or negative_returns.std() == 0:
            return 0
        return returns.mean() / negative_returns.std() * np.sqrt(252 * 24 * 12)

    def _calculate_consecutive_trades(self) -> Tuple[int, int]:
        """Calculate maximum consecutive wins and losses"""
        if not self.closed_trades:
            return 0, 0

        max_wins = 0
        max_losses = 0
        current_wins = 0
        current_losses = 0

        for trade in self.closed_trades:
            if trade.pnl > 0:
                current_wins += 1
                current_losses = 0
                max_wins = max(max_wins, current_wins)
            else:
                current_losses += 1
                current_wins = 0
                max_losses = max(max_losses, current_losses)

        return max_wins, max_losses


class BacktestReporter:
    """Generate backtest reports and visualizations"""

    def __init__(self):
        self.report_dir = Path("reports")
        self.report_dir.mkdir(exist_ok=True)

    def generate_report(self, results: BacktestResults, save_path: Optional[str] = None) -> str:
        """Generate comprehensive backtest report"""
        if save_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_path = str(self.report_dir / f"backtest_report_{timestamp}.html")

        # Create HTML report
        html_content = self._create_html_report(results)

        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"Backtest report saved to {save_path}")
        return str(save_path)

    def _create_html_report(self, results: BacktestResults) -> str:
        """Create HTML report content"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>MT5 Gold Trading Bot - Backtest Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
                .metric-card {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }}
                .metric-value {{ font-size: 24px; font-weight: bold; color: #007bff; }}
                .metric-label {{ font-size: 14px; color: #666; }}
                .positive {{ color: #28a745; }}
                .negative {{ color: #dc3545; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #f0f0f0; }}
                .trade-table {{ font-size: 12px; }}
                .trade-table th, .trade-table td {{ padding: 8px; }}
                .pagination {{ margin: 20px 0; text-align: center; }}
                .pagination button {{ margin: 0 5px; padding: 8px 12px; border: 1px solid #ddd; background: #f9f9f9; cursor: pointer; }}
                .pagination button:hover {{ background: #e9e9e9; }}
                .pagination button.active {{ background: #007bff; color: white; }}
                .trade-summary {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🤖 MT5 Gold Trading Bot - Backtest Report</h1>
                <p><strong>Period:</strong> {results.start_date.strftime('%Y-%m-%d')} to {results.end_date.strftime('%Y-%m-%d')}</p>
                <p><strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>

            <h2>📊 Performance Summary</h2>
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value {'positive' if results.total_return_pct > 0 else 'negative'}">
                        {results.total_return_pct:.2%}
                    </div>
                    <div class="metric-label">Total Return</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.total_trades}</div>
                    <div class="metric-label">Total Trades</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value {'positive' if results.win_rate > 0.5 else 'negative'}">
                        {results.win_rate:.1%}
                    </div>
                    <div class="metric-label">Win Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value negative">{results.max_drawdown_pct:.2%}</div>
                    <div class="metric-label">Max Drawdown</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.profit_factor:.2f}</div>
                    <div class="metric-label">Profit Factor</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{results.sharpe_ratio:.2f}</div>
                    <div class="metric-label">Sharpe Ratio</div>
                </div>
            </div>

            <h2>💰 Financial Metrics</h2>
            <table>
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Initial Balance</td><td>${results.initial_balance:,.2f}</td></tr>
                <tr><td>Final Balance</td><td>${results.final_balance:,.2f}</td></tr>
                <tr><td>Total Return</td><td>${results.total_return:,.2f}</td></tr>
                <tr><td>Average Win</td><td>${results.avg_win:,.2f}</td></tr>
                <tr><td>Average Loss</td><td>${results.avg_loss:,.2f}</td></tr>
                <tr><td>Max Drawdown</td><td>${results.max_drawdown:,.2f}</td></tr>
            </table>

            <h2>📈 Trading Statistics</h2>
            <table>
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Winning Trades</td><td>{results.winning_trades}</td></tr>
                <tr><td>Losing Trades</td><td>{results.losing_trades}</td></tr>
                <tr><td>Max Consecutive Wins</td><td>{results.max_consecutive_wins}</td></tr>
                <tr><td>Max Consecutive Losses</td><td>{results.max_consecutive_losses}</td></tr>
                <tr><td>Average Trade Duration</td><td>{results.avg_trade_duration:.1f} minutes</td></tr>
                <tr><td>Sortino Ratio</td><td>{results.sortino_ratio:.2f}</td></tr>
                <tr><td>Calmar Ratio</td><td>{results.calmar_ratio:.2f}</td></tr>
            </table>

            <h2>📊 Volume Analysis</h2>
            <table>
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Total Volume Traded</td><td>{results.total_volume:.2f} lots</td></tr>
                <tr><td>Average Volume per Trade</td><td>{results.avg_volume_per_trade:.2f} lots</td></tr>
                <tr><td>Maximum Volume per Trade</td><td>{results.max_volume_per_trade:.2f} lots</td></tr>
                <tr><td>Minimum Volume per Trade</td><td>{results.min_volume_per_trade:.2f} lots</td></tr>
                <tr><td>Volume Standard Deviation</td><td>{results.volume_std:.2f} lots</td></tr>
                <tr><td>Volume per Day (Avg)</td><td>{results.total_volume / max(1, (results.end_date - results.start_date).days):.2f} lots/day</td></tr>
            </table>

            <h2>📋 Recent Trades (Last 20)</h2>
            <table>
                <tr>
                    <th>Entry Time</th>
                    <th>Direction</th>
                    <th>Volume</th>
                    <th>Entry Price</th>
                    <th>Exit Price</th>
                    <th>Stop Loss</th>
                    <th>Take Profit</th>
                    <th>P&L</th>
                    <th>Duration</th>
                </tr>
                {self._generate_trades_table(results.trades)}
            </table>

            <h2>📋 Complete Trade Report</h2>
            <div class="trade-summary">
                <p><strong>Total Trades:</strong> {len(results.trades)} |
                   <strong>Winning:</strong> {results.winning_trades} |
                   <strong>Losing:</strong> {results.losing_trades} |
                   <strong>Win Rate:</strong> {results.win_rate:.1%}</p>
                <p><strong>Total P&L:</strong> ${sum(trade.pnl for trade in results.trades):,.2f} |
                   <strong>Average P&L:</strong> ${sum(trade.pnl for trade in results.trades) / len(results.trades):,.2f} |
                   <strong>Best Trade:</strong> ${max(trade.pnl for trade in results.trades):,.2f} |
                   <strong>Worst Trade:</strong> ${min(trade.pnl for trade in results.trades):,.2f}</p>
            </div>

            <div id="trade-table-container">
                {self._generate_complete_trades_table(results.trades)}
            </div>

            <h2>⏰ Trading Time Analysis</h2>
            {self._generate_time_analysis_summary(results)}
            <p>The following charts show trading activity patterns across different time periods:</p>
            <ul>
                <li><strong>trading_time_analysis.png</strong> - Trading activity by hour, day of week, month, and daily timeline</li>
                <li><strong>hourly_pnl_analysis.png</strong> - P&L analysis by hour of day</li>
                <li><strong>daily_pnl_analysis.png</strong> - P&L analysis by day of week</li>
            </ul>
            <p>These visualizations help identify the most profitable trading times and optimize strategy timing.</p>

            <h2>📊 Volume Analysis</h2>
            <p>The following charts show volume analysis and its relationship with performance:</p>
            <ul>
                <li><strong>volume_analysis.png</strong> - Volume distribution, volume vs P&L, hourly volume, and daily volume timeline</li>
                <li><strong>volume_performance_analysis.png</strong> - P&L analysis by volume buckets</li>
                <li><strong>volume_duration_analysis.png</strong> - Volume vs duration relationship and daily volume patterns</li>
            </ul>
            <p>These visualizations help understand the relationship between position sizing and performance.</p>

            <script>
                // Pagination functionality for trade table
                function showTradePage(page) {{
                    const rows = document.querySelectorAll('.trade-row');
                    const rowsPerPage = 50;
                    const startIndex = (page - 1) * rowsPerPage;
                    const endIndex = startIndex + rowsPerPage;

                    // Hide all rows
                    rows.forEach(row => row.style.display = 'none');

                    // Show rows for current page
                    for (let i = startIndex; i < endIndex && i < rows.length; i++) {{
                        rows[i].style.display = '';
                    }}

                    // Update pagination buttons
                    updatePagination(page, Math.ceil(rows.length / rowsPerPage));
                }}

                function updatePagination(currentPage, totalPages) {{
                    const pagination = document.getElementById('pagination');
                    pagination.innerHTML = '';

                    // Previous button
                    if (currentPage > 1) {{
                        const prevBtn = document.createElement('button');
                        prevBtn.textContent = '← Previous';
                        prevBtn.onclick = () => showTradePage(currentPage - 1);
                        pagination.appendChild(prevBtn);
                    }}

                    // Page numbers
                    for (let i = 1; i <= totalPages; i++) {{
                        const btn = document.createElement('button');
                        btn.textContent = i;
                        btn.onclick = () => showTradePage(i);
                        if (i === currentPage) {{
                            btn.className = 'active';
                        }}
                        pagination.appendChild(btn);
                    }}

                    // Next button
                    if (currentPage < totalPages) {{
                        const nextBtn = document.createElement('button');
                        nextBtn.textContent = 'Next →';
                        nextBtn.onclick = () => showTradePage(currentPage + 1);
                        pagination.appendChild(nextBtn);
                    }}
                }}

                // Initialize pagination
                window.onload = function() {{
                    showTradePage(1);
                }};
            </script>

            <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
                <p><strong>Disclaimer:</strong> Past performance is not indicative of future results.
                Trading involves significant risk of loss.</p>
            </footer>
        </body>
        </html>
        """

    def _generate_trades_table(self, trades: List[Trade]) -> str:
        """Generate HTML table rows for trades"""
        # Sort trades by entry_time and get the most recent 20
        sorted_trades = sorted(trades, key=lambda x: x.entry_time)[-20:]

        rows = []
        for trade in sorted_trades:
            pnl_class = 'positive' if trade.pnl > 0 else 'negative'
            rows.append(f"""
                <tr>
                    <td>{trade.entry_time.strftime('%Y-%m-%d %H:%M')}</td>
                    <td>{trade.direction.upper()}</td>
                    <td>{trade.volume:.2f}</td>
                    <td>{trade.entry_price:.5f}</td>
                    <td>{trade.exit_price:.5f}</td>
                    <td>{trade.stop_loss_price:.5f}</td>
                    <td>{trade.take_profit_price:.5f}</td>
                    <td class="{pnl_class}">${trade.pnl:.2f}</td>
                    <td>{trade.duration_minutes} min</td>
                </tr>
            """)
        return ''.join(rows)

    def _generate_complete_trades_table(self, trades: List[Trade]) -> str:
        """Generate complete HTML table for all trades with pagination"""
        if not trades:
            return "<p>No trades available.</p>"

        # Sort trades by entry_time (oldest first)
        sorted_trades = sorted(trades, key=lambda x: x.entry_time)

        # Create table header
        table_html = f"""
        <div class="pagination" id="pagination"></div>
        <table class="trade-table">
            <tr>
                <th>#</th>
                <th>Entry Time</th>
                <th>Exit Time</th>
                <th>Direction</th>
                <th>Volume</th>
                <th>Entry Price</th>
                <th>Exit Price</th>
                <th>Stop Loss</th>
                <th>Take Profit</th>
                <th>P&L</th>
                <th>P&L %</th>
                <th>Duration</th>
                <th>Entry Reason</th>
                <th>Exit Reason</th>
                <th>Max Favorable</th>
                <th>Max Adverse</th>
            </tr>
        """

        # Add trade rows
        for i, trade in enumerate(sorted_trades, 1):
            pnl_class = 'positive' if trade.pnl > 0 else 'negative'
            pnl_pct_class = 'positive' if trade.pnl_pct > 0 else 'negative'

            table_html += f"""
            <tr class="trade-row" style="display: {'none' if i > 50 else ''}">
                <td>{i}</td>
                <td>{trade.entry_time.strftime('%Y-%m-%d %H:%M')}</td>
                <td>{trade.exit_time.strftime('%Y-%m-%d %H:%M')}</td>
                <td>{trade.direction.upper()}</td>
                <td>{trade.volume:.2f}</td>
                <td>{trade.entry_price:.5f}</td>
                <td>{trade.exit_price:.5f}</td>
                <td>{trade.stop_loss_price:.5f}</td>
                <td>{trade.take_profit_price:.5f}</td>
                <td class="{pnl_class}">${trade.pnl:+.2f}</td>
                <td class="{pnl_pct_class}">{trade.pnl_pct:+.2%}</td>
                <td>{trade.duration_minutes} min</td>
                <td>{trade.entry_reason}</td>
                <td>{trade.exit_reason}</td>
                <td>${trade.max_favorable_excursion:.2f}</td>
                <td>${trade.max_adverse_excursion:.2f}</td>
            </tr>
            """

        table_html += "</table>"
        return table_html

    def _generate_time_analysis_summary(self, results: BacktestResults) -> str:
        """Generate time analysis summary for HTML report"""
        if not results.trades:
            return "<p>No trades available for time analysis.</p>"

        # Create DataFrame from trades
        trade_df = pd.DataFrame([asdict(trade) for trade in results.trades])
        trade_df['entry_time'] = pd.to_datetime(trade_df['entry_time'])

        # Extract time components
        trade_df['hour'] = trade_df['entry_time'].dt.hour
        trade_df['day_of_week'] = trade_df['entry_time'].dt.day_name()
        trade_df['month'] = trade_df['entry_time'].dt.month_name()

        # Calculate statistics
        hourly_stats = trade_df.groupby('hour')['pnl'].agg(['sum', 'mean', 'count']).round(2)
        daily_stats = trade_df.groupby('day_of_week')['pnl'].agg(['sum', 'mean', 'count']).round(2)
        monthly_stats = trade_df.groupby('month')['pnl'].agg(['sum', 'mean', 'count']).round(2)

        # Find best performing times
        try:
            best_hour = hourly_stats['sum'].idxmax() if len(hourly_stats) > 0 else "N/A"
            best_hour_pnl = hourly_stats.loc[best_hour, 'sum'] if best_hour != "N/A" else 0
            worst_hour = hourly_stats['sum'].idxmin() if len(hourly_stats) > 0 else "N/A"
            worst_hour_pnl = hourly_stats.loc[worst_hour, 'sum'] if worst_hour != "N/A" else 0
        except (AttributeError, IndexError):
            best_hour = "N/A"
            best_hour_pnl = 0
            worst_hour = "N/A"
            worst_hour_pnl = 0

        try:
            best_day = daily_stats['sum'].idxmax() if len(daily_stats) > 0 else "N/A"
            best_day_pnl = daily_stats.loc[best_day, 'sum'] if best_day != "N/A" else 0
            worst_day = daily_stats['sum'].idxmin() if len(daily_stats) > 0 else "N/A"
            worst_day_pnl = daily_stats.loc[worst_day, 'sum'] if worst_day != "N/A" else 0
        except (AttributeError, IndexError):
            best_day = "N/A"
            best_day_pnl = 0
            worst_day = "N/A"
            worst_day_pnl = 0

        # Most active times
        try:
            most_active_hour = hourly_stats['count'].idxmax() if len(hourly_stats) > 0 else "N/A"
            most_active_hour_count = hourly_stats.loc[most_active_hour, 'count'] if most_active_hour != "N/A" else 0
        except (AttributeError, IndexError):
            most_active_hour = "N/A"
            most_active_hour_count = 0

        try:
            most_active_day = daily_stats['count'].idxmax() if len(daily_stats) > 0 else "N/A"
            most_active_day_count = daily_stats.loc[most_active_day, 'count'] if most_active_day != "N/A" else 0
        except (AttributeError, IndexError):
            most_active_day = "N/A"
            most_active_day_count = 0

        return f"""
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-value positive">Hour {best_hour}</div>
                    <div class="metric-label">Best Hour (${best_hour_pnl:.0f})</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value negative">Hour {worst_hour}</div>
                    <div class="metric-label">Worst Hour (${worst_hour_pnl:.0f})</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value positive">{best_day}</div>
                    <div class="metric-label">Best Day (${best_day_pnl:.0f})</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value negative">{worst_day}</div>
                    <div class="metric-label">Worst Day (${worst_day_pnl:.0f})</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">Hour {most_active_hour}</div>
                    <div class="metric-label">Most Active Hour ({most_active_hour_count} trades)</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">{most_active_day}</div>
                    <div class="metric-label">Most Active Day ({most_active_day_count} trades)</div>
                </div>
            </div>
        """

    def create_visualizations(self, results: BacktestResults, save_dir: str = None):
        """Create backtest visualization charts"""
        if save_dir is None:
            save_dir = self.report_dir
        else:
            save_dir = Path(save_dir)
            save_dir.mkdir(exist_ok=True)

        # Set style
        plt.style.use('seaborn-v0_8')

        # 1. Equity Curve
        self._plot_equity_curve(results, save_dir)

        # 2. Drawdown Chart
        self._plot_drawdown(results, save_dir)

        # 3. Monthly Returns Heatmap
        self._plot_monthly_returns(results, save_dir)

        # 4. Trade Analysis
        self._plot_trade_analysis(results, save_dir)

        # 5. Trading Time Analysis
        self._plot_trading_time_analysis(results, save_dir)

        # 6. Volume Analysis
        self._plot_volume_analysis(results, save_dir)

        logger.info(f"Visualizations saved to {save_dir}")

    def _plot_equity_curve(self, results: BacktestResults, save_dir: Path):
        """Plot equity curve"""
        fig, ax = plt.subplots(figsize=(12, 6))

        ax.plot(results.equity_curve.index, results.equity_curve.values,
                linewidth=2, color='blue', label='Equity')
        ax.axhline(y=results.initial_balance, color='gray', linestyle='--',
                  alpha=0.7, label='Initial Balance')

        ax.set_title('Equity Curve', fontsize=16, fontweight='bold')
        ax.set_xlabel('Time')
        ax.set_ylabel('Equity ($)')
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(save_dir / 'equity_curve.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_drawdown(self, results: BacktestResults, save_dir: Path):
        """Plot drawdown chart"""
        fig, ax = plt.subplots(figsize=(12, 6))

        ax.fill_between(results.drawdown_curve.index,
                       results.drawdown_curve.values * 100, 0,
                       color='red', alpha=0.3, label='Drawdown')
        ax.plot(results.drawdown_curve.index,
               results.drawdown_curve.values * 100,
               color='red', linewidth=1)

        ax.set_title('Drawdown Chart', fontsize=16, fontweight='bold')
        ax.set_xlabel('Time')
        ax.set_ylabel('Drawdown (%)')
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(save_dir / 'drawdown.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_monthly_returns(self, results: BacktestResults, save_dir: Path):
        """Plot monthly returns heatmap"""
        # Calculate monthly returns
        monthly_returns = results.equity_curve.resample('ME').last().pct_change().dropna()

        if len(monthly_returns) < 2:
            return  # Not enough data for monthly analysis

        # Create pivot table for heatmap
        monthly_data = pd.DataFrame({
            'Year': monthly_returns.index.year,
            'Month': monthly_returns.index.month,
            'Return': monthly_returns.values * 100
        })

        pivot_table = monthly_data.pivot(index='Year', columns='Month', values='Return')

        fig, ax = plt.subplots(figsize=(12, 8))
        sns.heatmap(pivot_table, annot=True, fmt='.1f', cmap='RdYlGn',
                   center=0, ax=ax, cbar_kws={'label': 'Return (%)'})

        ax.set_title('Monthly Returns Heatmap', fontsize=16, fontweight='bold')
        ax.set_xlabel('Month')
        ax.set_ylabel('Year')

        plt.tight_layout()
        plt.savefig(save_dir / 'monthly_returns.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_trade_analysis(self, results: BacktestResults, save_dir: Path):
        """Plot trade analysis charts"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))

        # 1. P&L Distribution
        pnls = [trade.pnl for trade in results.trades]
        ax1.hist(pnls, bins=30, alpha=0.7, color='blue', edgecolor='black')
        ax1.axvline(x=0, color='red', linestyle='--', alpha=0.7)
        ax1.set_title('P&L Distribution')
        ax1.set_xlabel('P&L ($)')
        ax1.set_ylabel('Frequency')

        # 2. Trade Duration Distribution
        durations = [trade.duration_minutes for trade in results.trades]
        ax2.hist(durations, bins=30, alpha=0.7, color='green', edgecolor='black')
        ax2.set_title('Trade Duration Distribution')
        ax2.set_xlabel('Duration (minutes)')
        ax2.set_ylabel('Frequency')

        # 3. Cumulative P&L
        cumulative_pnl = np.cumsum(pnls)
        ax3.plot(range(len(cumulative_pnl)), cumulative_pnl, linewidth=2, color='purple')
        ax3.set_title('Cumulative P&L by Trade')
        ax3.set_xlabel('Trade Number')
        ax3.set_ylabel('Cumulative P&L ($)')
        ax3.grid(True, alpha=0.3)

        # 4. Win/Loss Ratio by Month
        trade_df = pd.DataFrame([asdict(trade) for trade in results.trades])
        trade_df['entry_time'] = pd.to_datetime(trade_df['entry_time'])
        trade_df['month'] = trade_df['entry_time'].dt.to_period('M')

        monthly_stats = trade_df.groupby('month').agg({
            'pnl': ['count', lambda x: (x > 0).sum(), lambda x: (x < 0).sum()]
        }).round(2)

        if len(monthly_stats) > 0:
            monthly_stats.columns = ['Total', 'Wins', 'Losses']
            monthly_stats['Win_Rate'] = monthly_stats['Wins'] / monthly_stats['Total'] * 100

            ax4.bar(range(len(monthly_stats)), monthly_stats['Win_Rate'],
                   alpha=0.7, color='orange', edgecolor='black')
            ax4.axhline(y=50, color='red', linestyle='--', alpha=0.7)
            ax4.set_title('Monthly Win Rate')
            ax4.set_xlabel('Month')
            ax4.set_ylabel('Win Rate (%)')
            ax4.set_xticks(range(len(monthly_stats)))
            ax4.set_xticklabels([str(m) for m in monthly_stats.index], rotation=45)

        plt.tight_layout()
        plt.savefig(save_dir / 'trade_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_trading_time_analysis(self, results: BacktestResults, save_dir: Path):
        """Plot trading time analysis charts"""
        if not results.trades:
            logger.warning("No trades available for time analysis")
            return

        # Create DataFrame from trades
        trade_df = pd.DataFrame([asdict(trade) for trade in results.trades])
        trade_df['entry_time'] = pd.to_datetime(trade_df['entry_time'])

        # Extract time components
        trade_df['hour'] = trade_df['entry_time'].dt.hour
        trade_df['day_of_week'] = trade_df['entry_time'].dt.day_name()
        trade_df['month'] = trade_df['entry_time'].dt.month_name()
        trade_df['date'] = trade_df['entry_time'].dt.date

        # Create subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 1. Trading Activity by Hour
        hourly_counts = trade_df['hour'].value_counts().sort_index()
        colors = plt.cm.viridis(np.linspace(0, 1, len(hourly_counts)))

        bars1 = ax1.bar(hourly_counts.index, hourly_counts.values,
                       color=colors, alpha=0.7, edgecolor='black')
        ax1.set_title('Trading Activity by Hour of Day', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Hour of Day')
        ax1.set_ylabel('Number of Trades')
        ax1.set_xticks(range(0, 24))
        ax1.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{int(height)}', ha='center', va='bottom', fontsize=10)

        # 2. Trading Activity by Day of Week
        day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        daily_counts = trade_df['day_of_week'].value_counts().reindex(day_order, fill_value=0)
        colors2 = plt.cm.Set3(np.linspace(0, 1, len(daily_counts)))

        bars2 = ax2.bar(range(len(daily_counts)), daily_counts.values,
                       color=colors2, alpha=0.7, edgecolor='black')
        ax2.set_title('Trading Activity by Day of Week', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Day of Week')
        ax2.set_ylabel('Number of Trades')
        ax2.set_xticks(range(len(daily_counts)))
        ax2.set_xticklabels(daily_counts.index, rotation=45)
        ax2.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{int(height)}', ha='center', va='bottom', fontsize=10)

        # 3. Trading Activity by Month
        month_order = ['January', 'February', 'March', 'April', 'May', 'June',
                      'July', 'August', 'September', 'October', 'November', 'December']
        monthly_counts = trade_df['month'].value_counts().reindex(month_order, fill_value=0)
        colors3 = plt.cm.Pastel1(np.linspace(0, 1, len(monthly_counts)))

        bars3 = ax3.bar(range(len(monthly_counts)), monthly_counts.values,
                       color=colors3, alpha=0.7, edgecolor='black')
        ax3.set_title('Trading Activity by Month', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Month')
        ax3.set_ylabel('Number of Trades')
        ax3.set_xticks(range(len(monthly_counts)))
        ax3.set_xticklabels(monthly_counts.index, rotation=45)
        ax3.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar in bars3:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{int(height)}', ha='center', va='bottom', fontsize=10)

        # 4. Trading Activity Timeline (Daily)
        daily_counts = trade_df['date'].value_counts().sort_index()
        ax4.plot(daily_counts.index, daily_counts.values,
                linewidth=2, color='purple', marker='o', markersize=4)
        ax4.set_title('Daily Trading Activity Timeline', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Date')
        ax4.set_ylabel('Number of Trades per Day')
        ax4.grid(True, alpha=0.3)

        # Rotate x-axis labels for better readability
        ax4.tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(save_dir / 'trading_time_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

        # Create additional detailed analysis
        self._create_detailed_time_analysis(trade_df, save_dir)

    def _create_detailed_time_analysis(self, trade_df: pd.DataFrame, save_dir: Path):
        """Create additional detailed time analysis charts"""

        # 1. Hourly P&L Analysis
        hourly_pnl = trade_df.groupby('hour')['pnl'].agg(['sum', 'mean', 'count']).round(2)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # Hourly P&L Sum
        colors = plt.cm.RdYlGn(np.linspace(0, 1, len(hourly_pnl)))
        bars1 = ax1.bar(hourly_pnl.index, hourly_pnl['sum'],
                       color=colors, alpha=0.7, edgecolor='black')
        ax1.set_title('Total P&L by Hour of Day', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Hour of Day')
        ax1.set_ylabel('Total P&L ($)')
        ax1.set_xticks(range(0, 24))
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # Add value labels on bars
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + (0.01 * max(hourly_pnl['sum'])),
                    f'${height:.0f}', ha='center', va='bottom' if height >= 0 else 'top',
                    fontsize=9, fontweight='bold')

        # Hourly Average P&L
        bars2 = ax2.bar(hourly_pnl.index, hourly_pnl['mean'],
                       color=colors, alpha=0.7, edgecolor='black')
        ax2.set_title('Average P&L by Hour of Day', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Hour of Day')
        ax2.set_ylabel('Average P&L ($)')
        ax2.set_xticks(range(0, 24))
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # Add value labels on bars
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + (0.01 * max(abs(hourly_pnl['mean']))),
                    f'${height:.1f}', ha='center', va='bottom' if height >= 0 else 'top',
                    fontsize=9, fontweight='bold')

        plt.tight_layout()
        plt.savefig(save_dir / 'hourly_pnl_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 2. Day of Week P&L Analysis
        day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        daily_pnl = trade_df.groupby('day_of_week')['pnl'].agg(['sum', 'mean', 'count']).reindex(day_order, fill_value=0).round(2)

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # Daily P&L Sum
        colors = plt.cm.Set3(np.linspace(0, 1, len(daily_pnl)))
        bars1 = ax1.bar(range(len(daily_pnl)), daily_pnl['sum'],
                       color=colors, alpha=0.7, edgecolor='black')
        ax1.set_title('Total P&L by Day of Week', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Day of Week')
        ax1.set_ylabel('Total P&L ($)')
        ax1.set_xticks(range(len(daily_pnl)))
        ax1.set_xticklabels(daily_pnl.index, rotation=45)
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # Add value labels on bars
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + (0.01 * max(abs(daily_pnl['sum']))),
                    f'${height:.0f}', ha='center', va='bottom' if height >= 0 else 'top',
                    fontsize=9, fontweight='bold')

        # Daily Average P&L
        bars2 = ax2.bar(range(len(daily_pnl)), daily_pnl['mean'],
                       color=colors, alpha=0.7, edgecolor='black')
        ax2.set_title('Average P&L by Day of Week', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Day of Week')
        ax2.set_ylabel('Average P&L ($)')
        ax2.set_xticks(range(len(daily_pnl)))
        ax2.set_xticklabels(daily_pnl.index, rotation=45)
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # Add value labels on bars
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + (0.01 * max(abs(daily_pnl['mean']))),
                    f'${height:.1f}', ha='center', va='bottom' if height >= 0 else 'top',
                    fontsize=9, fontweight='bold')

        plt.tight_layout()
        plt.savefig(save_dir / 'daily_pnl_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_volume_analysis(self, results: BacktestResults, save_dir: Path):
        """Plot volume analysis charts"""
        if not results.trades:
            logger.warning("No trades available for volume analysis")
            return

        # Create DataFrame from trades
        trade_df = pd.DataFrame([asdict(trade) for trade in results.trades])
        trade_df['entry_time'] = pd.to_datetime(trade_df['entry_time'])

        # Extract time components
        trade_df['hour'] = trade_df['entry_time'].dt.hour
        trade_df['day_of_week'] = trade_df['entry_time'].dt.day_name()
        trade_df['date'] = trade_df['entry_time'].dt.date

        # Create subplots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 1. Volume Distribution
        volumes = [trade.volume for trade in results.trades]
        ax1.hist(volumes, bins=30, alpha=0.7, color='blue', edgecolor='black')
        ax1.axvline(x=results.avg_volume_per_trade, color='red', linestyle='--',
                   alpha=0.7, label=f'Avg: {results.avg_volume_per_trade:.2f}')
        ax1.set_title('Volume Distribution per Trade', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Volume (lots)')
        ax1.set_ylabel('Frequency')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. Volume vs P&L Scatter Plot
        pnls = [trade.pnl for trade in results.trades]
        colors = ['green' if pnl > 0 else 'red' for pnl in pnls]
        ax2.scatter(volumes, pnls, c=colors, alpha=0.6, s=30)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.set_title('Volume vs P&L Relationship', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Volume (lots)')
        ax2.set_ylabel('P&L ($)')
        ax2.grid(True, alpha=0.3)

        # 3. Average Volume by Hour
        hourly_volume = trade_df.groupby('hour')['volume'].agg(['mean', 'sum']).round(3)
        bars = ax3.bar(hourly_volume.index, hourly_volume['mean'],
                      alpha=0.7, color='orange', edgecolor='black')
        ax3.set_title('Average Volume by Hour of Day', fontsize=14, fontweight='bold')
        ax3.set_xlabel('Hour of Day')
        ax3.set_ylabel('Average Volume (lots)')
        ax3.set_xticks(range(0, 24))
        ax3.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar in bars:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        # 4. Daily Volume Timeline
        daily_volume = trade_df.groupby('date')['volume'].sum()
        ax4.plot(daily_volume.index, daily_volume.values,
                linewidth=2, color='purple', marker='o', markersize=4)
        ax4.set_title('Daily Total Volume Timeline', fontsize=14, fontweight='bold')
        ax4.set_xlabel('Date')
        ax4.set_ylabel('Total Volume (lots)')
        ax4.grid(True, alpha=0.3)
        ax4.tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(save_dir / 'volume_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

        # Create additional volume analysis
        self._create_detailed_volume_analysis(trade_df, save_dir)

    def _create_detailed_volume_analysis(self, trade_df: pd.DataFrame, save_dir: Path):
        """Create additional detailed volume analysis charts"""

        # 1. Volume Performance Analysis
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # Volume buckets analysis
        volume_bins = pd.cut(trade_df['volume'], bins=5, labels=['Very Low', 'Low', 'Medium', 'High', 'Very High'])
        volume_performance = trade_df.groupby(volume_bins)['pnl'].agg(['sum', 'mean', 'count']).round(2)

        # Volume Performance by Bucket
        colors = plt.cm.viridis(np.linspace(0, 1, len(volume_performance)))
        bars1 = ax1.bar(range(len(volume_performance)), volume_performance['sum'],
                       color=colors, alpha=0.7, edgecolor='black')
        ax1.set_title('Total P&L by Volume Bucket', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Volume Bucket')
        ax1.set_ylabel('Total P&L ($)')
        ax1.set_xticks(range(len(volume_performance)))
        ax1.set_xticklabels(volume_performance.index, rotation=45)
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # Add value labels on bars
        for bar in bars1:
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + (0.01 * max(abs(volume_performance['sum']))),
                    f'${height:.0f}', ha='center', va='bottom' if height >= 0 else 'top',
                    fontsize=9, fontweight='bold')

        # Average P&L by Volume Bucket
        bars2 = ax2.bar(range(len(volume_performance)), volume_performance['mean'],
                       color=colors, alpha=0.7, edgecolor='black')
        ax2.set_title('Average P&L by Volume Bucket', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Volume Bucket')
        ax2.set_ylabel('Average P&L ($)')
        ax2.set_xticks(range(len(volume_performance)))
        ax2.set_xticklabels(volume_performance.index, rotation=45)
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # Add value labels on bars
        for bar in bars2:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + (0.01 * max(abs(volume_performance['mean']))),
                    f'${height:.1f}', ha='center', va='bottom' if height >= 0 else 'top',
                    fontsize=9, fontweight='bold')

        plt.tight_layout()
        plt.savefig(save_dir / 'volume_performance_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 2. Volume vs Duration Analysis
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # Volume vs Duration Scatter
        ax1.scatter(trade_df['volume'], trade_df['duration_minutes'],
                   alpha=0.6, s=30, c=trade_df['pnl'], cmap='RdYlGn')
        ax1.set_title('Volume vs Trade Duration', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Volume (lots)')
        ax1.set_ylabel('Duration (minutes)')
        ax1.grid(True, alpha=0.3)

        # Add colorbar
        scatter = ax1.scatter(trade_df['volume'], trade_df['duration_minutes'],
                            c=trade_df['pnl'], cmap='RdYlGn', alpha=0.6, s=30)
        plt.colorbar(scatter, ax=ax1, label='P&L ($)')

        # Volume by Day of Week
        day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        daily_volume_stats = trade_df.groupby('day_of_week')['volume'].agg(['mean', 'sum']).reindex(day_order, fill_value=0)

        bars = ax2.bar(range(len(daily_volume_stats)), daily_volume_stats['mean'],
                      alpha=0.7, color='lightblue', edgecolor='black')
        ax2.set_title('Average Volume by Day of Week', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Day of Week')
        ax2.set_ylabel('Average Volume (lots)')
        ax2.set_xticks(range(len(daily_volume_stats)))
        ax2.set_xticklabels(daily_volume_stats.index, rotation=45)
        ax2.grid(True, alpha=0.3)

        # Add value labels on bars
        for bar in bars:
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        plt.tight_layout()
        plt.savefig(save_dir / 'volume_duration_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
